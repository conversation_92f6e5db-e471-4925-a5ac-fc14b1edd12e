# frontend-docs 文档模板项目

## 文件目录

```
├── .vscode                   # vscode 配置(保存自动格式化)
├── docs                      # VitePress 文档主体目录
│   ├── index.md              # 文档首页
│   ├── .vitepress            # VitePress 配置和主题目录
│   │   ├── config.js         # VitePress 配置文件
│   │   └── theme             # 主题目录
│   │       ├── index.js      # 主题入口文件
│   │       └── style         # 主题样式目录
│   │           └── var.css   # 主题样式变量文件
│   ├── components            # 组件文档目录
│   │   ├── index.md          # 组件文档首页
│   │   ├── List.md           # List 组件文档
│   │   └── Table.md          # Table 组件文档
│   ├── guide                 # 指南文档目录
│   │   └── quickstart.md     # 快速入门指南
│   └── public                # 公共资源目录
│       └── logo.png          # 网站 Logo 图片
├── src                       # 项目源代码目录
│   ├── components            # 公共组件目录
│   │   ├── List.vue          # List 组件
│   │   └── Table.vue         # Table 组件
│   └── index.js              # 项目入口文件
├── .env.development          # 开发用环境变量
├── .env.production           # 打包环境变量
├── .eslintrc.cjs             # ESLint 规则
├── .gitignore                # Git 忽略目录
├── .npmrc                    # 依赖源
├── .prettierrc.json          # 格式化配置
├── package-lock.json         # 包含已安装 npm 依赖的精确版本信息
├── package.json              # 项目配置
└── README.md                 # 项目说明
```

## 安装依赖

```
npm install
```

### 开发

```
npm run start
```

### 打包

```
npm run build
```

# 登录私有仓库

```
npm login --registry=https://code.trechina.cn/package/repository/npm-hosted/
```

# 发布组件库

```
# 发布到公司仓库, 请确认 https://code.trechina.cn/package/ 角色权限
npm publish --registry=https://code.trechina.cn/package/repository/npm-hosted/
```
