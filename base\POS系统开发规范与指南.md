# POS系统开发规范与指南

## 📋 概述

本文档为POS收银系统的开发提供完整的规范和指南，确保代码质量、架构一致性和最佳实践的实施。

## 🏗️ 架构设计规范

### 系统架构原则

1. **分层清晰**: 表示层、业务层、数据层职责明确
2. **松耦合**: 各组件间依赖关系最小化
3. **高内聚**: 组件内部功能紧密相关
4. **可扩展**: 支持功能扩展和技术升级
5. **可测试**: 支持单元测试和集成测试

### 通信协议规范

#### JSON-RPC 2.0 通信标准

POS系统采用JSON-RPC 2.0作为C#与Vue之间的通信协议，包括方法调用和事件通知。

**适用场景**:
- Vue → C#: 硬件控制调用
- C# → Vue: 硬件状态通知、系统事件通知

**协议要求**:
- 所有消息必须包含 `"jsonrpc": "2.0"`
- 请求必须包含 `id`、`method`、可选 `params`
- 响应必须包含对应的 `id`，以及 `result` 或 `error`
- 通知必须包含 `method`，可选 `params`，**不得包含 `id`**

## 🔔 JSON-RPC 2.0 通知系统开发规范

### 1. 通知命名规范

#### 命名格式
```
{category}.{module}.{event}
```

#### 分类定义
- **hardware**: 硬件设备相关通知
- **system**: 系统状态相关通知  
- **business**: 业务流程相关通知
- **error**: 错误和异常通知

#### 命名示例
```typescript
// ✅ 正确命名
"hardware.scanner.barcodeScanned"     // 硬件-扫码枪-扫码成功
"hardware.printer.statusChanged"      // 硬件-打印机-状态变化
"system.network.statusChanged"        // 系统-网络-状态变化
"business.order.created"              // 业务-订单-创建完成
"error.occurred"                      // 错误-发生异常

// ❌ 错误命名
"scannerEvent"                        // 缺少分类层级
"hardware_scanner_scan"               // 使用下划线
"HARDWARE.SCANNER.SCAN"               // 全大写
"hardware.scanner"                    // 缺少事件名
```

### 2. C# 端通知实现规范

#### 基础服务接口
```csharp
/// <summary>
/// 通知服务接口
/// </summary>
public interface INotificationService
{
    /// <summary>
    /// 发送通知
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="method">通知方法名</param>
    /// <param name="parameters">通知参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task SendNotificationAsync<T>(string method, T parameters = default, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 发送硬件通知
    /// </summary>
    Task SendHardwareNotificationAsync<T>(string device, string eventName, T parameters = default);
    
    /// <summary>
    /// 发送系统通知
    /// </summary>
    Task SendSystemNotificationAsync<T>(string module, string eventName, T parameters = default);
    
    /// <summary>
    /// 发送错误通知
    /// </summary>
    Task SendErrorNotificationAsync(string source, string error, object details = null);
}
```

#### 通知参数规范
```csharp
/// <summary>
/// 所有通知参数必须包含的基础字段
/// </summary>
public abstract class NotificationParametersBase
{
    /// <summary>
    /// 通知时间戳 (Unix毫秒时间戳)
    /// </summary>
    [JsonPropertyName("timestamp")]
    public long Timestamp { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
}

/// <summary>
/// 硬件通知参数基类
/// </summary>
public abstract class HardwareNotificationParameters : NotificationParametersBase
{
    /// <summary>
    /// 设备名称
    /// </summary>
    [JsonPropertyName("deviceName")]
    public string DeviceName { get; set; }
}

/// <summary>
/// 扫码器通知参数
/// </summary>
public class ScannerNotificationParameters : HardwareNotificationParameters
{
    /// <summary>
    /// 条码内容
    /// </summary>
    [JsonPropertyName("barcode")]
    public string Barcode { get; set; }
    
    /// <summary>
    /// 条码类型 (可选)
    /// </summary>
    [JsonPropertyName("barcodeType")]
    public string BarcodeType { get; set; }
}

/// <summary>
/// 打印机状态通知参数
/// </summary>
public class PrinterStatusParameters : HardwareNotificationParameters
{
    /// <summary>
    /// 是否在线
    /// </summary>
    [JsonPropertyName("isOnline")]
    public bool IsOnline { get; set; }
    
    /// <summary>
    /// 纸张状态
    /// </summary>
    [JsonPropertyName("paperStatus")]
    public string PaperStatus { get; set; } // OK, EMPTY, LOW
    
    /// <summary>
    /// 错误信息 (可选)
    /// </summary>
    [JsonPropertyName("errorMessage")]
    public string ErrorMessage { get; set; }
}
```

#### 硬件服务实现规范
```csharp
/// <summary>
/// 扫码器通知服务
/// </summary>
public class ScannerNotificationService
{
    private readonly INotificationService _notificationService;
    private readonly ILogger<ScannerNotificationService> _logger;
    
    public ScannerNotificationService(
        INotificationService notificationService,
        ILogger<ScannerNotificationService> logger)
    {
        _notificationService = notificationService;
        _logger = logger;
    }
    
    /// <summary>
    /// 通知扫码成功
    /// </summary>
    public async Task NotifyBarcodeScannedAsync(string barcode, string deviceName, string barcodeType = null)
    {
        var parameters = new ScannerNotificationParameters
        {
            Barcode = barcode,
            DeviceName = deviceName,
            BarcodeType = barcodeType
        };
        
        await _notificationService.SendHardwareNotificationAsync("scanner", "barcodeScanned", parameters);
        _logger.LogInformation("扫码通知已发送: {Barcode} from {DeviceName}", barcode, deviceName);
    }
    
    /// <summary>
    /// 通知扫码错误
    /// </summary>
    public async Task NotifyScannerErrorAsync(string barcode, string error, string errorCode = null)
    {
        var parameters = new
        {
            barcode,
            error,
            errorCode = errorCode ?? "SCAN_FAILED",
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendHardwareNotificationAsync("scanner", "error", parameters);
        _logger.LogWarning("扫码错误通知: {Error} for barcode {Barcode}", error, barcode);
    }
    
    /// <summary>
    /// 通知连接状态变化
    /// </summary>
    public async Task NotifyConnectionChangedAsync(bool isConnected, string deviceName)
    {
        var parameters = new
        {
            isConnected,
            deviceName,
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };
        
        await _notificationService.SendHardwareNotificationAsync("scanner", "connectionChanged", parameters);
        _logger.LogInformation("扫码器连接状态: {IsConnected} - {DeviceName}", isConnected, deviceName);
    }
}
```

### 3. Vue 端监听实现规范

#### 通知监听器接口
```typescript
/// <summary>
/// 通知处理函数类型
/// </summary>
export type NotificationHandler<T = any> = (params: T) => void | Promise<void>

/// <summary>
/// 通知订阅接口
/// </summary>
export interface NotificationSubscription {
  unsubscribe(): void
}

/// <summary>
/// 通知监听器配置
/// </summary>
export interface NotificationListenerConfig {
  enableDebugLogging: boolean
  enableValidation: boolean
  enablePerformanceMonitoring: boolean
  maxRetryCount: number
  retryDelay: number
}
```

#### 类型安全的通知定义
```typescript
/// <summary>
/// 通知事件类型映射
/// </summary>
export interface NotificationEvents {
  // 硬件 - 扫码器
  'hardware.scanner.barcodeScanned': {
    barcode: string
    deviceName: string
    barcodeType?: string
    timestamp: number
  }
  
  'hardware.scanner.error': {
    barcode: string
    error: string
    errorCode: string
    timestamp: number
  }
  
  'hardware.scanner.connectionChanged': {
    isConnected: boolean
    deviceName: string
    timestamp: number
  }
  
  // 硬件 - 打印机
  'hardware.printer.statusChanged': {
    isOnline: boolean
    paperStatus: 'OK' | 'EMPTY' | 'LOW'
    errorMessage?: string
    deviceName: string
    timestamp: number
  }
  
  'hardware.printer.jobCompleted': {
    jobId: string
    orderId: string
    success: boolean
    deviceName: string
    timestamp: number
  }
  
  'hardware.printer.jobFailed': {
    orderId: string
    error: string
    errorCode: string
    deviceName: string
    timestamp: number
  }
  
  // 硬件 - 钱箱
  'hardware.cashDrawer.opened': {
    success: boolean
    deviceName: string
    timestamp: number
  }
  
  'hardware.cashDrawer.statusChanged': {
    isOpen: boolean
    deviceName: string
    timestamp: number
  }
  
  'hardware.cashDrawer.error': {
    error: string
    operation: string
    deviceName: string
    timestamp: number
  }
  
  // 系统通知
  'system.network.statusChanged': {
    isOnline: boolean
    connectionType?: 'WiFi' | 'Ethernet' | 'Mobile' | 'Unknown'
    timestamp: number
  }
  
  'system.app.updateAvailable': {
    version: string
    isForceUpdate: boolean
    description: string
    downloadUrl: string
    timestamp: number
  }
  
  'system.memory.warning': {
    usedMemory: number
    totalMemory: number
    usagePercentage: number
    timestamp: number
  }
  
  // 错误通知
  'error.occurred': {
    source: string
    error: string
    details?: any
    timestamp: number
  }
}

/// <summary>
/// 类型安全的通知处理函数
/// </summary>
export type TypedNotificationHandler<K extends keyof NotificationEvents> = 
  (params: NotificationEvents[K]) => void | Promise<void>
```

#### 组合式函数最佳实践
```typescript
/// <summary>
/// 硬件通知监听组合式函数
/// </summary>
export function useHardwareNotifications() {
  const { subscribe, subscribeCategory } = useNotifications()
  
  // 监听所有硬件事件
  const onHardwareEvent = (handler: NotificationHandler) => {
    return subscribeCategory('hardware', handler)
  }
  
  // 监听特定设备事件
  const onScannerEvent = (handler: NotificationHandler) => {
    return subscribeCategory('hardware.scanner', handler)
  }
  
  const onPrinterEvent = (handler: NotificationHandler) => {
    return subscribeCategory('hardware.printer', handler)
  }
  
  const onCashDrawerEvent = (handler: NotificationHandler) => {
    return subscribeCategory('hardware.cashDrawer', handler)
  }
  
  // 类型安全的特定事件监听
  const onBarcodeScanned = (handler: TypedNotificationHandler<'hardware.scanner.barcodeScanned'>) => {
    return subscribe('hardware.scanner.barcodeScanned', handler)
  }
  
  const onPrinterStatusChanged = (handler: TypedNotificationHandler<'hardware.printer.statusChanged'>) => {
    return subscribe('hardware.printer.statusChanged', handler)
  }
  
  return {
    onHardwareEvent,
    onScannerEvent,
    onPrinterEvent,
    onCashDrawerEvent,
    onBarcodeScanned,
    onPrinterStatusChanged
  }
}

/// <summary>
/// 系统通知监听组合式函数
/// </summary>
export function useSystemNotifications() {
  const { subscribe, subscribeCategory } = useNotifications()
  
  const onSystemEvent = (handler: NotificationHandler) => {
    return subscribeCategory('system', handler)
  }
  
  const onNetworkStatusChanged = (handler: TypedNotificationHandler<'system.network.statusChanged'>) => {
    return subscribe('system.network.statusChanged', handler)
  }
  
  const onUpdateAvailable = (handler: TypedNotificationHandler<'system.app.updateAvailable'>) => {
    return subscribe('system.app.updateAvailable', handler)
  }
  
  const onMemoryWarning = (handler: TypedNotificationHandler<'system.memory.warning'>) => {
    return subscribe('system.memory.warning', handler)
  }
  
  return {
    onSystemEvent,
    onNetworkStatusChanged,
    onUpdateAvailable,
    onMemoryWarning
  }
}
```

### 4. 错误处理规范

#### 标准错误代码
```typescript
enum JsonRpcErrorCode {
  // JSON-RPC 2.0 标准错误代码
  PARSE_ERROR = -32700,        // JSON解析错误
  INVALID_REQUEST = -32600,    // 无效请求
  METHOD_NOT_FOUND = -32601,   // 方法不存在
  INVALID_PARAMS = -32602,     // 参数无效
  INTERNAL_ERROR = -32603,     // 内部错误
  
  // 自定义错误代码 (-32000 到 -32099)
  HARDWARE_ERROR = -32000,     // 硬件错误
  NETWORK_ERROR = -32001,      // 网络错误
  CACHE_ERROR = -32002,        // 缓存错误
  PERMISSION_ERROR = -32003,   // 权限错误
  VALIDATION_ERROR = -32004,   // 验证错误
  TIMEOUT_ERROR = -32005,      // 超时错误
  DEVICE_BUSY = -32006,        // 设备忙碌
  DEVICE_OFFLINE = -32007,     // 设备离线
  CONFIGURATION_ERROR = -32008, // 配置错误
  LICENSE_ERROR = -32009       // 许可证错误
}
```

#### 错误处理最佳实践
```csharp
/// <summary>
/// 硬件服务异常处理示例
/// </summary>
public async Task<PrintResult> PrintTicketAsync(PrintTicketData data)
{
    try
    {
        // 验证参数
        ValidatePrintData(data);
        
        // 检查打印机状态
        var status = await GetPrinterStatusAsync();
        if (!status.IsOnline)
        {
            throw new HardwareException("打印机离线", JsonRpcErrorCode.DEVICE_OFFLINE);
        }
        
        if (status.PaperStatus == "EMPTY")
        {
            throw new HardwareException("打印机缺纸", JsonRpcErrorCode.HARDWARE_ERROR);
        }
        
        // 执行打印
        var result = await ExecutePrintAsync(data);
        
        // 发送成功通知
        await _notificationService.SendHardwareNotificationAsync("printer", "jobCompleted", new
        {
            jobId = result.JobId,
            orderId = data.OrderId,
            success = true,
            deviceName = GetDeviceName(),
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        });
        
        return result;
    }
    catch (HardwareException ex)
    {
        _logger.LogError(ex, "打印任务失败: {OrderId}", data.OrderId);
        
        // 发送失败通知
        await _notificationService.SendHardwareNotificationAsync("printer", "jobFailed", new
        {
            orderId = data.OrderId,
            error = ex.Message,
            errorCode = ex.ErrorCode.ToString(),
            deviceName = GetDeviceName(),
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        });
        
        throw new JsonRpcException(ex.ErrorCode, ex.Message, new { orderId = data.OrderId });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "打印任务异常: {OrderId}", data.OrderId);
        
        // 发送失败通知
        await _notificationService.SendErrorNotificationAsync("PrintService", ex.Message, new { orderId = data.OrderId });
        
        throw new JsonRpcException(JsonRpcErrorCode.INTERNAL_ERROR, "打印失败", new { orderId = data.OrderId });
    }
}
```

### 5. 性能和监控规范

#### 通知性能监控
```typescript
/// <summary>
/// 通知性能监控类
/// </summary>
export class NotificationPerformanceMonitor {
  private metrics = {
    totalNotifications: 0,
    processedNotifications: 0,
    failedNotifications: 0,
    averageProcessingTime: 0,
    peakProcessingTime: 0
  }
  
  startProcessing(method: string): () => void {
    const startTime = performance.now()
    this.metrics.totalNotifications++
    
    return () => {
      const processingTime = performance.now() - startTime
      this.recordProcessingTime(method, processingTime)
    }
  }
  
  recordSuccess() {
    this.metrics.processedNotifications++
  }
  
  recordFailure() {
    this.metrics.failedNotifications++
  }
  
  private recordProcessingTime(method: string, time: number) {
    if (time > this.metrics.peakProcessingTime) {
      this.metrics.peakProcessingTime = time
    }
    
    // 性能警告
    if (time > 100) { // 超过100ms
      console.warn(`🐌 Slow notification processing [${method}]: ${time.toFixed(2)}ms`)
    }
    
    // 更新平均时间 (滑动窗口)
    this.updateAverageTime(time)
  }
  
  getMetrics() {
    return {
      ...this.metrics,
      successRate: this.metrics.totalNotifications > 0 
        ? (this.metrics.processedNotifications / this.metrics.totalNotifications) * 100 
        : 0
    }
  }
}
```

### 6. 测试规范

#### 单元测试示例
```csharp
[TestClass]
public class ScannerNotificationServiceTests
{
    private Mock<INotificationService> _mockNotificationService;
    private Mock<ILogger<ScannerNotificationService>> _mockLogger;
    private ScannerNotificationService _service;
    
    [TestInitialize]
    public void Setup()
    {
        _mockNotificationService = new Mock<INotificationService>();
        _mockLogger = new Mock<ILogger<ScannerNotificationService>>();
        _service = new ScannerNotificationService(_mockNotificationService.Object, _mockLogger.Object);
    }
    
    [TestMethod]
    public async Task NotifyBarcodeScannedAsync_ShouldSendCorrectNotification()
    {
        // Arrange
        var barcode = "1234567890";
        var deviceName = "Scanner-001";
        var barcodeType = "EAN13";
        
        // Act
        await _service.NotifyBarcodeScannedAsync(barcode, deviceName, barcodeType);
        
        // Assert
        _mockNotificationService.Verify(x => 
            x.SendHardwareNotificationAsync("scanner", "barcodeScanned", 
                It.Is<ScannerNotificationParameters>(p => 
                    p.Barcode == barcode && 
                    p.DeviceName == deviceName && 
                    p.BarcodeType == barcodeType)), 
            Times.Once);
    }
    
    [TestMethod]
    public async Task NotifyScannerErrorAsync_ShouldIncludeDefaultErrorCode()
    {
        // Arrange
        var barcode = "invalid";
        var error = "Invalid barcode format";
        
        // Act
        await _service.NotifyScannerErrorAsync(barcode, error);
        
        // Assert
        _mockNotificationService.Verify(x => 
            x.SendHardwareNotificationAsync("scanner", "error", 
                It.Is<object>(p => 
                    p.GetType().GetProperty("errorCode").GetValue(p).ToString() == "SCAN_FAILED")), 
            Times.Once);
    }
}
```

### 7. 文档规范

#### 通知文档模板
```typescript
/**
 * 硬件通知: 扫码成功
 * 
 * @event hardware.scanner.barcodeScanned
 * @category 硬件
 * @subcategory 扫码器
 * @description 当扫码器成功识别条码时触发此通知
 * 
 * @param {string} barcode - 扫描到的条码内容
 * @param {string} deviceName - 扫码器设备名称
 * @param {string} [barcodeType] - 条码类型 (EAN13, QR_CODE, etc.)
 * @param {number} timestamp - 通知时间戳 (Unix毫秒)
 * 
 * @example
 * ```typescript
 * // 监听扫码成功事件
 * onBarcodeScanned(({ barcode, deviceName, barcodeType }) => {
 *   console.log(`扫码成功: ${barcode} 来自设备: ${deviceName}`)
 *   // 处理扫码结果...
 * })
 * ```
 * 
 * @example
 * ```json
 * // 通知消息格式
 * {
 *   "jsonrpc": "2.0",
 *   "method": "hardware.scanner.barcodeScanned",
 *   "params": {
 *     "barcode": "1234567890123",
 *     "deviceName": "Honeywell-Scanner-001", 
 *     "barcodeType": "EAN13",
 *     "timestamp": 1703123456789
 *   }
 * }
 * ```
 * 
 * @see {@link ScannerNotificationService.NotifyBarcodeScannedAsync} C#端发送方法
 * @see {@link useHardwareNotifications.onBarcodeScanned} Vue端监听方法
 * 
 * @since 1.0.0
 * @stability stable
 */
```

## 🛠️ 开发工具链规范

### 代码质量工具

#### ESLint 配置 (Vue项目)
```json
{
  "extends": [
    "@vue/typescript/recommended",
    "plugin:vue/vue3-recommended"
  ],
  "rules": {
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "error",
    "vue/component-name-in-template-casing": ["error", "PascalCase"],
    "vue/max-attributes-per-line": ["error", { "singleline": 3 }]
  }
}
```

#### EditorConfig 配置
```ini
[*.{ts,vue,cs}]
indent_style = space
indent_size = 2
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

[*.cs]
indent_size = 4
```

### 提交规范

#### Git Commit 消息格式
```
<type>[scope]: <subject>

[body]

[footer]
```

#### 类型说明
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式修改
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

#### 示例
```
feat(notification): 添加JSON-RPC 2.0通知系统

- 实现统一的通知发送服务
- 添加硬件事件监听功能
- 支持类型安全的事件订阅

BREAKING CHANGE: 移除旧的事件系统，需要迁移到新的通知API
```

## 📊 质量保证

### 代码审查清单

#### C# 代码审查
- [ ] 是否遵循命名约定
- [ ] 是否正确实现异常处理
- [ ] 是否添加必要的日志记录
- [ ] 是否包含单元测试
- [ ] 是否正确使用依赖注入
- [ ] 通知参数是否包含时间戳
- [ ] 是否遵循JSON-RPC 2.0规范

#### Vue/TypeScript 代码审查
- [ ] 是否使用组合式API
- [ ] 是否正确定义TypeScript类型
- [ ] 是否遵循Vue 3最佳实践
- [ ] 是否正确处理组件生命周期
- [ ] 通知监听器是否正确清理
- [ ] 是否使用类型安全的通知订阅

### 性能基准

#### 通知系统性能要求
- 通知发送延迟: < 10ms
- 通知处理时间: < 50ms
- 内存占用增长: < 1MB/小时
- 错误率: < 0.1%

这套完整的开发规范确保了POS系统的代码质量、架构一致性和JSON-RPC 2.0通知系统的标准化实现。 