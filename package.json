{"name": "frontend-test", "version": "1.0.0", "private": false, "type": "module", "main": "src/index.js", "files": ["src"], "scripts": {"start": "vitepress dev docs --mode development", "build": "vitepress build docs --mode production", "serve": "vitepress serve docs --mode development", "release": "npm run build && npm publish", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@vue/eslint-config-prettier": "^7.0.0", "eslint": "^8.38.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.11.0", "prettier": "^2.8.7", "vitepress": "1.0.0-alpha.65", "vue": "3.2.44"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["@algolia/client-search"]}}}