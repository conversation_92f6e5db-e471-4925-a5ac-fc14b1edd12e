# POS系统项目配置示例

## 📋 概述

本文档提供 POS 系统的完整配置示例，包括 C# 端、Vue 端以及统一 JSON-RPC 2.0 桥接系统的详细配置。

## 🏗️ 项目结构

```
POSSystem/
├── POSSystem.Desktop/                 # C# WPF 项目
│   ├── POSSystem.Core/
│   ├── POSSystem.Infrastructure/
│   ├── POSSystem.WPF/
│   └── POSSystem.Tests/
├── pos-frontend/                      # Vue3 前端项目
│   ├── src/
│   │   ├── bridge/                    # JSON-RPC 桥接
│   │   ├── composables/               # 组合式函数
│   │   ├── components/                # 组件
│   │   ├── views/                     # 页面
│   │   ├── services/                  # API 服务
│   │   └── types/                     # 类型定义
│   ├── public/
│   ├── package.json
│   └── vite.config.ts
└── docs/                              # 文档
```

## ⚙️ C# 端配置

### 1. 依赖注入配置

```csharp
// App.xaml.cs
public partial class App : Application
{
    private ServiceProvider serviceProvider;
    
    protected override async void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);
        
        var services = new ServiceCollection();
        ConfigureServices(services);
        serviceProvider = services.BuildServiceProvider();
        
        // 启动主窗口
        var mainWindow = serviceProvider.GetRequiredService<MainWindow>();
        mainWindow.Show();
    }
    
    private void ConfigureServices(IServiceCollection services)
    {
        // 日志配置
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog(CreateLogger());
        });
        
        // 配置管理
        var configuration = BuildConfiguration();
        services.AddSingleton<IConfiguration>(configuration);
        
        // 数据库配置
        services.AddDbContext<CacheDbContext>(options =>
        {
            var dbPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "POSSystem", "cache.db");
            options.UseSqlite($"Data Source={dbPath}");
        });
        
        // HTTP 客户端配置
        services.AddHttpClient<IJavaMidtierProxy>(client =>
        {
            client.BaseAddress = new Uri(configuration["JavaMidtier:BaseUrl"]);
            client.Timeout = TimeSpan.FromMilliseconds(
                configuration.GetValue<int>("JavaMidtier:Timeout", 30000));
            client.DefaultRequestHeaders.Add("X-API-Key", 
                configuration["JavaMidtier:ApiKey"]);
        });
        
        // 🔔 JSON-RPC 通知系统配置
        services.AddSingleton<INotificationService, NotificationService>();
        
        // 硬件通知服务
        services.AddSingleton<ScannerNotificationService>();
        services.AddSingleton<PrinterNotificationService>();
        services.AddSingleton<CashDrawerNotificationService>();
        
        // 系统通知服务
        services.AddSingleton<SystemNotificationService>();
        
        // 硬件服务
        services.AddSingleton<IScannerService, ScannerService>();
        services.AddSingleton<IPrintService, PrintService>();
        services.AddSingleton<ICashDrawerService, CashDrawerService>();
        
        // JSON-RPC 处理器
        services.AddSingleton<JsonRpcHandler>();
        
        // 其他服务
        services.AddSingleton<IUpdateService, UpdateService>();
        services.AddTransient<MainWindow>();
        services.AddAutoMapper(typeof(MappingProfile));
    }
    
    private ILogger CreateLogger()
    {
        return new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.File("logs/pos-.log", 
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 30)
            .WriteTo.Console()
            .CreateLogger();
    }
    
    private IConfiguration BuildConfiguration()
    {
        return new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json", optional: false)
            .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ENVIRONMENT") ?? "Production"}.json", optional: true)
            .AddEnvironmentVariables()
            .Build();
    }
}
```

### 2. appsettings.json 配置

```json
{
  "JavaMidtier": {
    "BaseUrl": "http://localhost:8080",
    "ApiKey": "your-api-key-here",
    "Timeout": 30000,
    "RetryCount": 3,
    "RetryDelay": 1000
  },
  
  "Hardware": {
    "Scanner": {
      "Type": "USB",
      "Port": "COM3",
      "BaudRate": 9600,
      "Timeout": 5000,
      "AutoConnect": true
    },
    "Printer": {
      "Type": "Thermal",
      "Name": "POS-80",
      "Port": "USB001",
      "PaperWidth": 80,
      "Encoding": "UTF-8"
    },
    "CashDrawer": {
      "Type": "Electronic",
      "Port": "COM4",
      "OpenDuration": 500
    }
  },
  
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "System": "Warning",
      "POSSystem": "Debug"
    },
    "EnableRemoteLogging": true,
    "RemoteEndpoint": "http://localhost:8080/api/logs",
    "UploadInterval": 6,
    "MaxLogFileSize": "10MB",
    "RetainedFileCountLimit": 30
  },
  
  "Notifications": {
    "EnableDebugLogging": true,
    "MaxRetryCount": 3,
    "RetryDelay": 1000,
    "EnableValidation": true,
    "EnablePerformanceMonitoring": true
  },
  
  "Update": {
    "CheckInterval": "24:00:00",
    "UpdateServer": "https://update.yourcompany.com",
    "EnableAutoUpdate": true,
    "EnableGrayRelease": true,
    "BackupRetentionDays": 7
  },
  
  "Cache": {
    "ExpirationHours": 24,
    "MaxItems": 10000,
    "EnableCompression": true,
    "SyncInterval": "01:00:00"
  },
  
  "UI": {
    "Theme": "Light",
    "Language": "zh-CN",
    "EnableAnimations": true,
    "AutoHideMenu": false,
    "ShowHardwareStatus": true
  }
}
```

### 3. MainWindow.xaml.cs 配置

```csharp
// MainWindow.xaml.cs
public partial class MainWindow : Window
{
    private readonly JsonRpcHandler _jsonRpcHandler;
    private readonly ILogger<MainWindow> _logger;
    private readonly IConfiguration _configuration;
    
    public MainWindow(
        JsonRpcHandler jsonRpcHandler,
        ILogger<MainWindow> logger,
        IConfiguration configuration)
    {
        InitializeComponent();
        
        _jsonRpcHandler = jsonRpcHandler;
        _logger = logger;
        _configuration = configuration;
        
        InitializeWebView();
    }
    
    private async void InitializeWebView()
    {
        try
        {
            // WebView2 环境配置
            var webView2Environment = await CoreWebView2Environment.CreateAsync(
                browserExecutableFolder: null,
                userDataFolder: Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "POSSystem", "WebView2"),
                options: new CoreWebView2EnvironmentOptions()
                {
                    AdditionalBrowserArguments = "--disable-web-security --allow-running-insecure-content"
                });
            
            await WebViewContainer.EnsureCoreWebView2Async(webView2Environment);
            
            // 配置WebView2设置
            WebViewContainer.CoreWebView2.Settings.IsScriptEnabled = true;
            WebViewContainer.CoreWebView2.Settings.AreDevToolsEnabled = 
                _configuration.GetValue<bool>("EnableDevTools", false);
            WebViewContainer.CoreWebView2.Settings.AreHostObjectsAllowed = false;
            WebViewContainer.CoreWebView2.Settings.IsWebMessageEnabled = true;
            
            // 初始化JSON-RPC处理器
            _jsonRpcHandler.Initialize(WebViewContainer);
            
            // 导航到Vue应用
            var frontendUrl = _configuration.GetValue<string>("Frontend:Url", "http://localhost:5173");
            WebViewContainer.CoreWebView2.Navigate(frontendUrl);
            
            _logger.LogInformation("WebView2 initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize WebView2");
            MessageBox.Show($"WebView2 initialization failed: {ex.Message}", 
                "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
```

## 🎨 Vue 端配置

### 1. package.json

```json
{
  "name": "pos-frontend",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite --host 0.0.0.0 --port 5173",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "type-check": "vue-tsc --noEmit"
  },
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.5",
    "pinia": "^2.1.7",
    "axios": "^1.6.0",
    "tdesign-vue-next": "^1.7.0",
    "tdesign-icons-vue-next": "^0.2.0",
    "@vueuse/core": "^10.5.0",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "@types/node": "^20.9.0",
    "@types/uuid": "^9.0.7",
    "@typescript-eslint/eslint-plugin": "^6.11.0",
    "@typescript-eslint/parser": "^6.11.0",
    "@vitejs/plugin-vue": "^4.4.1",
    "eslint": "^8.54.0",
    "eslint-plugin-vue": "^9.18.1",
    "prettier": "^3.1.0",
    "typescript": "^5.2.0",
    "vite": "^5.0.0",
    "vue-tsc": "^1.8.22"
  }
}
```

### 2. vite.config.ts

```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/views': resolve(__dirname, 'src/views'),
      '@/services': resolve(__dirname, 'src/services'),
      '@/types': resolve(__dirname, 'src/types'),
      '@/bridge': resolve(__dirname, 'src/bridge'),
      '@/composables': resolve(__dirname, 'src/composables'),
    }
  },
  
  server: {
    host: '0.0.0.0',
    port: 5173,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        timeout: 30000
      }
    }
  },
  
  build: {
    target: 'esnext',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  },
  
  define: {
    __VUE_OPTIONS_API__: false,
    __VUE_PROD_DEVTOOLS__: false
  }
})
```

### 3. src/main.ts

```typescript
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import TDesign from 'tdesign-vue-next'

import App from './App.vue'
import routes from './router'

// 样式导入
import 'tdesign-vue-next/es/style/index.css'
import './styles/index.scss'

// 初始化通知管理器（确保在应用启动时就初始化）
import './bridge/notification-listener'

const app = createApp(App)

// 路由配置
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 状态管理
const pinia = createPinia()

// 插件注册
app.use(router)
app.use(pinia)
app.use(TDesign)

// 全局配置
app.config.globalProperties.$PRODUCTION = import.meta.env.PROD

// 错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
  
  // 可以在这里上报错误到C#端
  if (window.chrome?.webview) {
    const errorNotification = {
      jsonrpc: '2.0',
      method: 'frontend.error.occurred',
      params: {
        error: err.message,
        stack: err.stack,
        info,
        timestamp: Date.now()
      }
    }
    
    window.chrome.webview.postMessage(JSON.stringify(errorNotification))
  }
}

app.mount('#app')
```

### 4. 环境配置 (.env 文件)

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8080
VITE_APP_TITLE=POS收银系统 - 开发环境
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=true
VITE_LOG_LEVEL=debug

# .env.production  
VITE_API_BASE_URL=https://api.yourcompany.com
VITE_APP_TITLE=POS收银系统
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=false
VITE_LOG_LEVEL=error

# .env.test
VITE_API_BASE_URL=http://test-api.yourcompany.com
VITE_APP_TITLE=POS收银系统 - 测试环境
VITE_ENABLE_MOCK=true
VITE_ENABLE_DEBUG=true
VITE_LOG_LEVEL=info
```

## 🔔 JSON-RPC 通知系统配置

### 1. 通知配置接口

```typescript
// src/config/notification.ts
export interface NotificationConfig {
  enableDebugLogging: boolean
  maxRetryCount: number
  retryDelay: number
  enableValidation: boolean
  enablePerformanceMonitoring: boolean
  batchSize: number
  flushInterval: number
}

export const defaultNotificationConfig: NotificationConfig = {
  enableDebugLogging: import.meta.env.VITE_ENABLE_DEBUG === 'true',
  maxRetryCount: 3,
  retryDelay: 1000,
  enableValidation: true,
  enablePerformanceMonitoring: true,
  batchSize: 50,
  flushInterval: 100
}

// 运行时配置更新
export class NotificationConfigManager {
  private static config = { ...defaultNotificationConfig }
  
  static updateConfig(updates: Partial<NotificationConfig>) {
    Object.assign(this.config, updates)
    this.notifyConfigChange()
  }
  
  static getConfig(): NotificationConfig {
    return { ...this.config }
  }
  
  private static notifyConfigChange() {
    document.dispatchEvent(new CustomEvent('notification-config-changed', {
      detail: this.config
    }))
  }
}
```

### 2. 通知性能监控

```typescript
// src/bridge/notification-monitor.ts
export class NotificationPerformanceMonitor {
  private metrics = {
    totalNotifications: 0,
    processedNotifications: 0,
    failedNotifications: 0,
    averageProcessingTime: 0,
    lastProcessingTime: 0,
    peakProcessingTime: 0
  }
  
  private processingTimes: number[] = []
  
  startProcessing(method: string): () => void {
    const startTime = performance.now()
    
    return () => {
      const processingTime = performance.now() - startTime
      this.recordProcessingTime(method, processingTime)
    }
  }
  
  private recordProcessingTime(method: string, time: number) {
    this.metrics.totalNotifications++
    this.metrics.lastProcessingTime = time
    
    if (time > this.metrics.peakProcessingTime) {
      this.metrics.peakProcessingTime = time
    }
    
    this.processingTimes.push(time)
    
    // 保持最近100个记录
    if (this.processingTimes.length > 100) {
      this.processingTimes.shift()
    }
    
    // 计算平均时间
    this.metrics.averageProcessingTime = 
      this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length
    
    // 性能警告
    if (time > 100) { // 超过100ms
      console.warn(`Slow notification processing [${method}]: ${time.toFixed(2)}ms`)
    }
  }
  
  recordSuccess() {
    this.metrics.processedNotifications++
  }
  
  recordFailure() {
    this.metrics.failedNotifications++
  }
  
  getMetrics() {
    return {
      ...this.metrics,
      successRate: this.metrics.totalNotifications > 0 
        ? (this.metrics.processedNotifications / this.metrics.totalNotifications) * 100 
        : 0
    }
  }
  
  reset() {
    this.metrics = {
      totalNotifications: 0,
      processedNotifications: 0,
      failedNotifications: 0,
      averageProcessingTime: 0,
      lastProcessingTime: 0,
      peakProcessingTime: 0
    }
    this.processingTimes = []
  }
}

export const notificationMonitor = new NotificationPerformanceMonitor()
```

### 3. 通知批处理配置

```typescript
// src/bridge/notification-batch.ts
export class NotificationBatchProcessor {
  private queue: Array<{ method: string, params: any, timestamp: number }> = []
  private timer: number | null = null
  private config = NotificationConfigManager.getConfig()
  
  constructor() {
    // 监听配置变化
    document.addEventListener('notification-config-changed', (event: any) => {
      this.config = event.detail
    })
  }
  
  addNotification(method: string, params: any) {
    this.queue.push({
      method,
      params,
      timestamp: Date.now()
    })
    
    if (this.queue.length >= this.config.batchSize) {
      this.flush()
    } else if (!this.timer) {
      this.timer = window.setTimeout(() => {
        this.flush()
      }, this.config.flushInterval)
    }
  }
  
  private flush() {
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }
    
    if (this.queue.length === 0) return
    
    const batch = this.queue.splice(0)
    this.processBatch(batch)
  }
  
  private async processBatch(notifications: Array<{ method: string, params: any, timestamp: number }>) {
    for (const notification of notifications) {
      try {
        // 处理单个通知
        await this.processNotification(notification)
        notificationMonitor.recordSuccess()
      } catch (error) {
        console.error('Batch notification processing failed:', error)
        notificationMonitor.recordFailure()
      }
    }
  }
  
  private async processNotification(notification: { method: string, params: any, timestamp: number }) {
    const endTiming = notificationMonitor.startProcessing(notification.method)
    
    try {
      // 验证通知
      if (this.config.enableValidation) {
        this.validateNotification(notification)
      }
      
      // 触发处理器
      document.dispatchEvent(new CustomEvent(notification.method, {
        detail: notification.params
      }))
      
    } finally {
      endTiming()
    }
  }
  
  private validateNotification(notification: { method: string, params: any }) {
    if (!notification.method) {
      throw new Error('Missing notification method')
    }
    
    if (!notification.params?.timestamp) {
      console.warn(`Missing timestamp in notification: ${notification.method}`)
    }
  }
}
```

## 🛠️ 构建和部署配置

### 1. 构建脚本

```bash
#!/bin/bash
# build.sh

echo "开始构建 POS 系统..."

# 构建 Vue 前端
echo "构建前端..."
cd pos-frontend
npm ci
npm run build
cd ..

# 构建 C# 后端
echo "构建后端..."
dotnet restore POSSystem.Desktop
dotnet build POSSystem.Desktop --configuration Release --no-restore

# 复制前端资源到 C# 项目
echo "复制前端资源..."
cp -r pos-frontend/dist/* POSSystem.Desktop/POSSystem.WPF/wwwroot/

# 发布应用
echo "发布应用..."
dotnet publish POSSystem.Desktop/POSSystem.WPF --configuration Release --self-contained true --runtime win-x64 --output ./publish

echo "构建完成！输出目录: ./publish"
```

### 2. Docker 配置 (可选)

```dockerfile
# Dockerfile.frontend
FROM node:18-alpine AS build

WORKDIR /app
COPY pos-frontend/package*.json ./
RUN npm ci --only=production

COPY pos-frontend/ .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
```

这套完整的配置确保了POS系统的各个组件能够协同工作，特别是JSON-RPC 2.0通知系统的正确配置和高效运行。 

---

## 🌟 统一 JSON-RPC 桥接系统配置更新

### 1. 最新桥接配置

```typescript
// src/config/bridge-config.ts
export interface UnifiedBridgeConfig {
  defaultTimeout: number
  maxRetries: number
  retryDelay: number
  enableDebugLogging: boolean
  enablePerformanceMonitoring: boolean
  enableValidation: boolean
  maxLogEntries: number
  autoReconnect: boolean
}

export const BRIDGE_PRESETS = {
  development: {
    defaultTimeout: 10000,
    maxRetries: 1,
    enableDebugLogging: true,
    enablePerformanceMonitoring: true,
    maxLogEntries: 2000,
    autoReconnect: true
  },
  
  production: {
    defaultTimeout: 30000,
    maxRetries: 3,
    enableDebugLogging: false,
    enablePerformanceMonitoring: false,
    maxLogEntries: 500,
    autoReconnect: true
  },
  
  testing: {
    defaultTimeout: 5000,
    maxRetries: 0,
    enableDebugLogging: true,
    enablePerformanceMonitoring: true,
    maxLogEntries: 5000,
    autoReconnect: false
  }
}

// 应用配置
export function applyBridgePreset(preset: keyof typeof BRIDGE_PRESETS) {
  const config = BRIDGE_PRESETS[preset]
  console.log(`🔧 Applying ${preset} bridge preset:`, config)
  return config
}
```

### 2. 应用初始化配置

```typescript
// src/main.ts
import { createApp } from 'vue'
import App from './App.vue'
import { setupUnifiedBridge } from '@/bridge/setup'
import { applyBridgePreset } from '@/config/bridge-config'

const app = createApp(App)

// 根据环境应用桥接预设
const preset = import.meta.env.DEV ? 'development' : 
              import.meta.env.VITE_MODE === 'testing' ? 'testing' : 
              'production'

const bridgeConfig = applyBridgePreset(preset)

// 初始化统一桥接
setupUnifiedBridge(bridgeConfig)

app.mount('#app')
```

### 3. 完整的桥接设置

```typescript
// src/bridge/setup.ts
import { native } from './index'
import type { UnifiedBridgeConfig } from '@/config/bridge-config'

export function setupUnifiedBridge(config: UnifiedBridgeConfig) {
  // 1. 应用全局配置
  applyGlobalConfiguration(config)
  
  // 2. 设置预设方法配置
  applyMethodConfigurations()
  
  // 3. 设置全局事件监听
  setupGlobalEventHandlers()
  
  // 4. 初始化性能监控
  if (config.enablePerformanceMonitoring) {
    setupPerformanceMonitoring()
  }
  
  // 5. 初始化开发工具
  if (config.enableDebugLogging) {
    setupDebugTools()
  }
  
  console.log('🚀 Unified JSON-RPC Bridge initialized successfully')
}

function applyGlobalConfiguration(config: UnifiedBridgeConfig) {
  // 配置全局超时
  native.configure('*', { 
    timeout: config.defaultTimeout,
    retries: config.maxRetries
  })
}

function applyMethodConfigurations() {
  // 快速响应方法（全局配置不需要返回值）
  const fireAndForgetMethods = [
    'printer.beep',
    'scanner.beep',
    'app.log',
    'system.notify',
    'debug.trace',
    'app.playSound'
  ]
  
  fireAndForgetMethods.forEach(method => {
    native.configure(method, { 
      requiresReturn: false,
      timeout: 1000
    })
  })
  
  // 长耗时方法（全局配置较长超时和重试）
  const longRunningMethods = {
    'printer.printTicket': { timeout: 60000, retries: 2 },
    'app.updateSoftware': { timeout: 300000, retries: 1 },
    'system.backup': { timeout: 180000, retries: 1 },
    'inventory.syncProducts': { timeout: 120000, retries: 2 }
  }
  
  Object.entries(longRunningMethods).forEach(([method, config]) => {
    native.configure(method, config)
  })
  
  // 设备状态查询方法（全局配置中等超时）
  const statusMethods = [
    'printer.getStatus',
    'scanner.getStatus', 
    'cashDrawer.getStatus',
    'app.getVersion',
    'system.getInfo'
  ]
  
  statusMethods.forEach(method => {
    native.configure(method, { 
      timeout: 5000,
      retries: 1
    })
  })
  
  // 批量操作方法（全局配置较短超时，通常配合单次配置使用）
  const batchMethods = [
    'inventory.updateStock',
    'local.updateStat',
    'cache.store',
    'log.write'
  ]
  
  batchMethods.forEach(method => {
    native.configure(method, {
      timeout: 3000,
      retries: 0,
      requiresReturn: false  // 批量操作默认不需要返回值
    })
  })
}

function setupGlobalEventHandlers() {
  // 全局错误处理
  native.on('error.occurred', (params) => {
    console.error(`🚨 系统错误 [${params.source}]:`, params.error)
    
    // 错误上报或用户提示
    if (params.severity === 'critical') {
      // 触发全局错误弹窗
      document.dispatchEvent(new CustomEvent('critical-error', {
        detail: params
      }))
    }
  })
  
  // 连接状态监听
  native.system.on('connectionStatusChanged', (params) => {
    const status = params.isConnected ? '已连接' : '已断开'
    console.log(`🔗 桥接连接状态: ${status}`)
    
    // 更新UI状态指示器
    document.dispatchEvent(new CustomEvent('bridge-connection-changed', {
      detail: params
    }))
  })
  
  // 性能警告监听
  native.on('performance.warning', (params) => {
    console.warn(`⚠️ 性能警告: ${params.method} 耗时 ${params.duration}ms`)
  })
}

function setupPerformanceMonitoring() {
  // 定期输出性能统计
  setInterval(() => {
    const methods = native.listeners()
    const stats = {
      活跃监听器数量: Array.isArray(methods) 
        ? methods.reduce((sum, method) => sum + native.listeners(method), 0)
        : 0,
      监听方法数: Array.isArray(methods) ? methods.length : 0,
      时间戳: new Date().toLocaleTimeString()
    }
    
    console.log('📊 桥接性能统计:', stats)
  }, 60000) // 每分钟输出一次
}

function setupDebugTools() {
  // 将 native 对象暴露到全局作用域（仅开发环境）
  if (typeof window !== 'undefined') {
    (window as any).__NATIVE_BRIDGE__ = native
    console.log('🛠️ Debug: native bridge exposed as window.__NATIVE_BRIDGE__')
  }
  
  // 监听所有事件（调试用）
  native.on('*', (params, context) => {
    console.log(`🔔 [DEBUG] Event: ${context.method}`, params)
  })
}

// 导出便捷函数
export function configureBridgeMethod(method: string, config: any) {
  native.configure(method, config)
}

export function getBridgeStats() {
  const methods = native.listeners()
  return {
    totalMethods: Array.isArray(methods) ? methods.length : 0,
    totalListeners: Array.isArray(methods) 
      ? methods.reduce((sum, method) => sum + native.listeners(method), 0)
      : 0,
    methods: methods
  }
}
```

### 4. 项目特定配置示例

```typescript
// src/config/pos-bridge-config.ts
import { configureBridgeMethod } from '@/bridge/setup'

// POS系统特定的桥接配置
export function setupPOSBridgeConfig() {
  
  // 收银相关配置（全局配置）
  configureBridgeMethod('cashier.processSale', {
    timeout: 30000,
    retries: 2,
    requiresReturn: true  // 收银操作必须确认结果
  })
  
  configureBridgeMethod('cashier.processRefund', {
    timeout: 45000,
    retries: 1,
    requiresReturn: true  // 退款操作必须确认结果
  })
  
  // 硬件设备特定配置（全局配置）
  configureBridgeMethod('scanner.continuousScan', {
    requiresReturn: false,
    timeout: 1000
  })
  
  configureBridgeMethod('printer.printReceipt', {
    timeout: 60000,
    retries: 3,
    requiresReturn: true  // 打印重要凭证需要确认
  })
  
  configureBridgeMethod('cashDrawer.openWithDelay', {
    timeout: 10000,
    retries: 2,
    requiresReturn: true  // 钱箱操作需要确认
  })
  
  // 库存管理配置（全局配置）
  configureBridgeMethod('inventory.syncProducts', {
    timeout: 120000,
    retries: 1,
    requiresReturn: true  // 同步操作需要确认结果
  })
  
  configureBridgeMethod('inventory.updateStock', {
    timeout: 15000,
    retries: 2,
    requiresReturn: false  // 单个库存更新默认不需要返回值（批量操作时可单次覆盖）
  })
  
  // 用户交互配置
  configureBridgeMethod('ui.showNotification', {
    requiresReturn: false,
    timeout: 2000
  })
  
  configureBridgeMethod('ui.showDialog', {
    timeout: 30000,  // 用户可能需要时间操作
    requiresReturn: true  // 需要用户选择结果
  })
  
  console.log('💼 POS specific bridge configuration applied')
}

// 单次配置使用示例
export function usePOSOperations() {
  
  // 快速操作：不等待返回的提示音
  const playQuickSound = (soundType: string) => {
    return native.app.playSound(soundType, { requiresReturn: false, timeout: 500 })
  }
  
  // 重要操作：需要确认结果的打印
  const printImportantDocument = (data: any) => {
    return native.printer.printReceipt(data, { 
      timeout: 90000,  // 单次配置更长超时
      retries: 5,      // 单次配置更多重试
      requiresReturn: true 
    })
  }
  
  // 批量库存更新：最后一个需要确认
  const batchUpdateInventory = async (items: any[]) => {
    const results = []
    
    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      const isLast = i === items.length - 1
      
      // 单次配置：只有最后一个需要返回值
      const result = await native.inventory.updateStock(item, {
        requiresReturn: isLast,
        timeout: isLast ? 10000 : 3000,  // 最后一个给更长时间
        retries: isLast ? 2 : 0          // 最后一个允许重试
      })
      
      results.push(result)
    }
    
    return results
  }
  
  // 条件配置：根据业务逻辑动态配置
  const processPayment = async (amount: number, method: string) => {
    const isLargeAmount = amount > 1000
    const isCardPayment = method !== 'CASH'
    
    // 大额或银行卡支付需要更严格的确认
    const config = (isLargeAmount || isCardPayment) ? {
      timeout: 60000,
      retries: 3,
      requiresReturn: true
    } : {
      timeout: 30000,
      retries: 1,
      requiresReturn: true
    }
    
    return native.payment.process({ amount, method }, config)
  }
  
  return {
    playQuickSound,
    printImportantDocument,
    batchUpdateInventory,
    processPayment
  }
}

// 在应用启动时调用
// setupPOSBridgeConfig()
```

### 5. 组合式函数最佳实践配置

```typescript
// src/composables/useBridgeConfig.ts
import { ref, readonly } from 'vue'
import { native } from '@/bridge'

export function useBridgeConfiguration() {
  const configHistory = ref<Array<{method: string; config: any; timestamp: number}>>([])
  
  const configure = (method: string, config: any) => {
    native.configure(method, config)
    
    // 记录配置历史
    configHistory.value.push({
      method,
      config: { ...config },
      timestamp: Date.now()
    })
    
    // 限制历史记录数量
    if (configHistory.value.length > 100) {
      configHistory.value.shift()
    }
  }
  
  const batchConfigure = (configs: Record<string, any>) => {
    Object.entries(configs).forEach(([method, config]) => {
      configure(method, config)
    })
  }
  
  const getConfiguration = (method: string) => {
    // 从历史记录中获取最近的配置
    const recent = configHistory.value
      .filter(item => item.method === method)
      .pop()
    
    return recent?.config || null
  }
  
  const resetConfiguration = () => {
    configHistory.value = []
    console.log('🔄 Bridge configuration reset')
  }
  
  return {
    configHistory: readonly(configHistory),
    configure,
    batchConfigure,
    getConfiguration,
    resetConfiguration
  }
}

// 性能监控组合式函数
export function useBridgePerformance() {
  const metrics = ref({
    callCount: 0,
    errorCount: 0,
    averageResponseTime: 0,
    lastResponseTime: 0
  })
  
  const startTimer = () => performance.now()
  
  const endTimer = (startTime: number, isError = false) => {
    const duration = performance.now() - startTime
    
    metrics.value.callCount++
    if (isError) metrics.value.errorCount++
    
    metrics.value.lastResponseTime = duration
    metrics.value.averageResponseTime = 
      (metrics.value.averageResponseTime * (metrics.value.callCount - 1) + duration) / 
      metrics.value.callCount
  }
  
  const resetMetrics = () => {
    metrics.value = {
      callCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
      lastResponseTime: 0
    }
  }
  
  return {
    metrics: readonly(metrics),
    startTimer,
    endTimer,
    resetMetrics
  }
}
```

这套完整的统一桥接配置系统为 POS 系统提供了：

- **配置预设**: 开发、测试、生产环境的预设配置
- **方法配置**: 针对不同类型方法的特定配置
- **性能监控**: 实时性能统计和警告
- **错误处理**: 统一的错误处理和上报机制
- **调试工具**: 开发环境的调试辅助功能
- **类型安全**: 完整的 TypeScript 类型支持

通过这种配置方式，开发团队可以根据不同的部署环境和业务需求，灵活地调整 JSON-RPC 桥接系统的行为，确保系统的稳定性和性能。 