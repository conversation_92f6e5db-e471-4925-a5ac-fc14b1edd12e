# 列表 ListComponent

### 示例

<ListComponent :items="listItems" />

<script setup>
import { ref } from 'vue';
import { ListComponent } from '../../src/index';

const listItems = ref(['项目1', '项目2', '项目3', '项目4', '项目5']);
</script>

### 代码

```Vue
<ListComponent :items="listItems" />

<script setup>
import { ref } from 'vue';
import { ListComponent } from '../../src/index';

const listItems = ref(['项目1', '项目2', '项目3', '项目4', '项目5']);
</script>
```

### 属性

| 属性  | 类型       | 是否必需 | 描述                       |
| ----- | ---------- | -------- | -------------------------- |
| items | 字符串数组 | 是       | 一个表示列表的字符串数组。 |
