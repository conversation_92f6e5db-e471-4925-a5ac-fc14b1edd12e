# 快速开始

### 安装 & 使用

```bash
# 配置公司 npm 源地址
npm config set registry https://code.trechina.cn/package/repository/npm/

# 安装工具类
npm install frontend-test@latest --save
```

或

```bash
# 指定单次安装源
npm install frontend-test@latest --save --registry https://code.trechina.cn/package/repository/npm/
```

## 组件库的发布

### 登录私有仓库

```bash
# 登录私有仓库
npm login --registry=https://code.trechina.cn/package/repository/npm-hosted/
```

### 发布组件库

```bash
# 发布组件库
# 如遇无法推送, 请确认 https://code.trechina.cn/package/ 角色权限
npm publish --registry=https://code.trechina.cn/package/repository/npm-hosted/
```
