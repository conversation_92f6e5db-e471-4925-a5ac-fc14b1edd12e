# 表格 TableComponent

### 示例

<TableComponent :headers="tableHeaders" :rows="tableRows" />

<script setup>
import { ref } from 'vue';
import { TableComponent } from '../../src/index';
5
const tableHeaders = ref(['姓名', '年龄', '职业']);
const tableRows = ref([
  ['张三', '25', '工程师'],
  ['李四', '30', '医生'],
  ['王五', '28', '律师']
]);
</script>

### 代码

```Vue
<TableComponent :headers="tableHeaders" :rows="tableRows" />

<script setup>
import { ref } from 'vue';
import { TableComponent } from '../../src/index';

const tableHeaders = ref(['姓名', '年龄', '职业']);
const tableRows = ref([
  ['张三', '25', '工程师'],
  ['李四', '30', '医生'],
  ['王五', '28', '律师']
]);
</script>
```

### 属性

| 属性    | 类型       | 是否必需 | 描述                                                 |
| ------- | ---------- | -------- | ---------------------------------------------------- |
| headers | 字符串数组 | 是       | 一个表示表格表头的字符串数组。                       |
| rows    | 数组的数组 | 是       | 一个数组的数组，其中每个数组包含作为字符串的行数据。 |
