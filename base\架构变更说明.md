# POS系统架构变更说明

## 📋 变更概述

基于您的反馈和实际业务需求，我们对POS系统架构进行了两项重要调整：

1. **SQLite角色重新定位**：从主数据库调整为本地缓存数据库
2. **通信协议升级**：从自定义消息格式升级为JSON-RPC 2.0标准协议

## 🔄 主要变更详解

### 1. SQLite角色调整

#### 变更前：主数据库架构
```
Vue前端 ← → C# WPF ← → SQLite主数据库
```

#### 变更后：本地缓存架构
```
Vue前端 ← → C# WPF桥接层 ← → Java中台API
                ↓
            SQLite本地缓存
            (离线支持)
```

#### 调整理由

**优势分析：**

| 方面 | 原架构 | 新架构 | 改进效果 |
|------|--------|--------|----------|
| **业务逻辑** | C#本地处理 | Java中台集中处理 | 统一业务规则，便于管理 |
| **数据一致性** | 多客户端同步复杂 | 中台统一数据源 | 数据一致性保证 |
| **功能扩展** | 需要客户端升级 | 中台服务升级 | 快速功能迭代 |
| **维护成本** | 分散在各客户端 | 集中在中台服务 | 降低维护复杂度 |
| **离线能力** | 完整离线功能 | 缓存支持的智能离线 | 平衡在线体验和离线需求 |

### 2. 通信协议升级

#### 变更前：自定义消息协议
```typescript
// 手动消息构建
const message = {
  id: generateId(),
  type: 'HARDWARE_CONTROL',
  action: 'print_receipt',
  data: orderData,
  timestamp: Date.now()
}
window.chrome.webview.postMessage(message)
```

#### 变更后：统一JSON-RPC 2.0桥接系统
```typescript
// 类型安全 + 灵活配置的方法调用
const result = await native.printer.printTicket({
  orderId: 'ORDER-001',
  content: '商品清单...'
}, {
  timeout: 45000,       // 单次配置：自定义超时
  retries: 3,           // 单次配置：重要操作多重试
  requiresReturn: true  // 单次配置：确保获得结果
})

// 全局配置 + 链式调用
native
  .configure('app.playSound', { requiresReturn: false })
  .configure('printer.beep', { requiresReturn: false, timeout: 1000 })

// 强化事件监听
const unsubscribe = native.scanner.on('barcodeScanned', (params) => {
  console.log('扫码:', params.barcode)
})
```

#### 升级优势

**技术对比：**

| 特性 | 自定义协议 | 统一JSON-RPC桥接 | 优势 |
|------|------------|-------------------|------|
| **类型安全** | ❌ 手动类型检查 | ✅ 完整TypeScript类型推导 | 编译时错误检测 |
| **标准化** | ❌ 自定义格式 | ✅ JSON-RPC 2.0国际标准 | 通用性和互操作性 |
| **调试能力** | ❌ 消息追踪困难 | ✅ 标准化错误码+性能监控 | 更好的问题诊断 |
| **开发效率** | ❌ 手动序列化 | ✅ 动态代理+智能参数识别 | 减少样板代码 |
| **版本兼容** | ❌ 破坏性变更风险 | ✅ 向前兼容设计 | 平滑升级路径 |
| **配置灵活性** | ❌ 硬编码配置 | ✅ 全局配置+单次配置 | 精确控制每次调用 |
| **事件系统** | ❌ DOM事件监听 | ✅ 多层级事件+自动清理 | 强大的事件处理能力 |
| **性能优化** | ❌ 无优化机制 | ✅ 批量操作+性能监控 | 高效的调用策略 |

## 🏗️ 新架构详细设计

### 1. 本地缓存策略

```csharp
public class LocalCacheService : ILocalCacheService
{
    // 1. 商品信息缓存（24小时有效期）
    public async Task<Product> GetCachedProductAsync(string barcode)
    {
        return await _context.CachedProducts
            .Where(p => p.Barcode == barcode && p.ExpiryTime > DateTime.UtcNow)
            .FirstOrDefaultAsync();
    }
    
    // 2. 离线订单暂存
    public async Task CacheOfflineOrderAsync(Order order)
    {
        order.SyncStatus = SyncStatus.Pending;
        await _context.PendingOrders.AddAsync(order);
        await _context.SaveChangesAsync();
    }
    
    // 3. 系统配置本地存储
    public async Task SaveConfigAsync(string key, object value)
    {
        var config = await _context.SystemConfigs
            .FirstOrDefaultAsync(c => c.Key == key);
            
        if (config != null)
        {
            config.Value = JsonSerializer.Serialize(value);
        }
        else
        {
            _context.SystemConfigs.Add(new SystemConfig
            {
                Key = key,
                Value = JsonSerializer.Serialize(value)
            });
        }
        
        await _context.SaveChangesAsync();
    }
}
```

### 2. Java中台集成

```csharp
public class JavaMidtierProxy : IJavaMidtierProxy
{
    private readonly HttpClient _httpClient;
    private readonly ILocalCacheService _cacheService;
    
    public async Task<Product> GetProductByBarcodeAsync(string barcode)
    {
        try
        {
            // 1. 调用Java中台API
            var response = await _httpClient.GetAsync($"/api/products/barcode/{barcode}");
            response.EnsureSuccessStatusCode();
            
            var product = await response.Content.ReadFromJsonAsync<Product>();
            
            // 2. 更新本地缓存
            if (product != null)
            {
                await _cacheService.UpdateProductCacheAsync(product);
            }
            
            return product;
        }
        catch (HttpRequestException)
        {
            // 3. 网络异常时使用本地缓存
            return await _cacheService.GetCachedProductAsync(barcode);
        }
    }
    
    public async Task<Order> CreateOrderAsync(Order order)
    {
        var request = new CreateOrderRequest
        {
            Items = order.Items.Select(i => new OrderItemRequest
            {
                ProductId = i.ProductId,
                Quantity = i.Quantity,
                Price = i.Price
            }).ToList(),
            PaymentMethod = order.PaymentMethod,
            TotalAmount = order.TotalAmount,
            DeviceId = GetDeviceId(),
            StoreId = GetStoreId()
        };
        
        var response = await _httpClient.PostAsJsonAsync("/api/orders", request);
        response.EnsureSuccessStatusCode();
        
        return await response.Content.ReadFromJsonAsync<Order>();
    }
}
```

### 3. 统一JSON-RPC桥接接口设计

```typescript
// 方法配置接口
interface MethodConfig {
  requiresReturn?: boolean;  // 是否需要返回值，默认true
  timeout?: number;          // 超时时间，默认30000ms  
  retries?: number;          // 重试次数，默认0
}

// 完整的统一API接口定义
export interface NativeApi {
  // 硬件控制（支持单次配置）
  printer: {
    printTicket(data: PrintTicketRequest, config?: MethodConfig): Promise<PrintResult>
    getStatus(config?: MethodConfig): Promise<PrinterStatus>
    cancelJob(jobId: string, config?: MethodConfig): Promise<boolean>
    
    // 事件监听
    on(event: string, handler: (...args: any[]) => void): () => void
    once(event: string, handler: (...args: any[]) => void): () => void
    off(event: string, handler?: (...args: any[]) => void): void
  }
  
  scanner: {
    getStatus(config?: MethodConfig): Promise<ScannerStatus>
    startScan(config?: MethodConfig): Promise<void>
    stopScan(config?: MethodConfig): Promise<void>
    testConnection(config?: MethodConfig): Promise<boolean>
    
    // 事件监听
    on(event: string, handler: (...args: any[]) => void): () => void
    once(event: string, handler: (...args: any[]) => void): () => void
    off(event: string, handler?: (...args: any[]) => void): void
  }
  
  cashDrawer: {
    open(params?: any, config?: MethodConfig): Promise<boolean>
    getStatus(config?: MethodConfig): Promise<DrawerStatus>
    
    // 事件监听
    on(event: string, handler: (...args: any[]) => void): () => void
    once(event: string, handler: (...args: any[]) => void): () => void
    off(event: string, handler?: (...args: any[]) => void): void
  }
  
  // 系统功能
  app: {
    getVersion(config?: MethodConfig): Promise<string>
    restart(config?: MethodConfig): void
    getSystemInfo(config?: MethodConfig): Promise<SystemInfo>
    exportLogs(config?: MethodConfig): Promise<string>
    playSound(type: string, config?: MethodConfig): Promise<void>
    
    // 事件监听
    on(event: string, handler: (...args: any[]) => void): () => void
  }
  
  // 库存管理
  inventory: {
    updateStock(item: any, config?: MethodConfig): Promise<any>
    syncProducts(config?: MethodConfig): Promise<SyncResult>
  }
  
  // 本地统计
  local: {
    updateStat(stat: any, config?: MethodConfig): Promise<void>
  }
  
  // 支付处理
  payment: {
    process(data: any, config?: MethodConfig): Promise<any>
  }
  
  // 全局配置和监听
  configure(method: string, config: MethodConfig): NativeApi  // 链式调用
  on(method: string, handler: (...args: any[]) => void): () => void
  once(method: string, handler: (...args: any[]) => void): () => void
  off(method: string, handler?: (...args: any[]) => void): void
  listeners(method?: string): string[] | number
}

// 类型定义
interface PrintTicketRequest {
  orderId: string
  items: OrderItem[]
  totalAmount: number
  paymentMethod: string
  customerInfo?: CustomerInfo
}

interface PrintResult {
  success: boolean
  jobId: string
  error?: string
}

interface SyncResult {
  syncedCount: number
  failedCount: number
  errors?: string[]
}
```

## 🔧 实际应用示例

### 1. 统一桥接的扫码收银流程

```typescript
// Vue组件中的使用
async function handleBarcodeScan(barcode: string) {
  try {
    // 扫码成功时播放提示音（不需要等待返回）
    native.app.playSound('beep', { requiresReturn: false, timeout: 500 })
    
    // 直接调用HTTP API查询商品
    const product = await productApi.getByBarcode(barcode)
    
    if (product) {
      addToCart(product)
      showMessage('商品已添加到购物车', 'success')
      
      // 添加成功提示音（单次配置）
      native.app.playSound('success', { requiresReturn: false, timeout: 500 })
    } else {
      showMessage('商品不存在', 'warning')
      
      // 错误提示音
      native.app.playSound('error', { requiresReturn: false, timeout: 500 })
    }
  } catch (error) {
    console.error('处理扫码失败:', error)
    showMessage('系统错误，请重试', 'error')
  }
}

// 使用统一事件监听系统
const unsubscribe = native.scanner.on('barcodeScanned', (params) => {
  const { barcode, deviceName } = params
  console.log(`设备 ${deviceName} 扫码:`, barcode)
  
  // 自动处理扫码
  handleBarcodeScan(barcode)
})

// 组件卸载时自动清理
onUnmounted(() => {
  unsubscribe()
})
```

### 2. 统一桥接的订单处理

```typescript
async function createOrder(orderData: CreateOrderRequest) {
  try {
    // 结账开始提示音（立即执行，不等待）
    native.app.playSound('checkout-start', { requiresReturn: false })
    
    // 1. 直接调用Java中台创建订单
    const order = await orderApi.create(orderData)
    
    // 2. 打印小票（重要操作需要确认返回值和较长超时）
    await native.printer.printTicket({
      orderId: order.id,
      items: order.items,
      totalAmount: order.totalAmount,
      paymentMethod: order.paymentMethod
    }, { 
      timeout: 60000,    // 打印可能需要较长时间
      retries: 3,        // 重要操作多重试几次
      requiresReturn: true // 确保获得打印结果
    })
    
    // 3. 打开钱箱（需要确认是否成功）
    await native.cashDrawer.open({}, { timeout: 5000, requiresReturn: true })
    
    // 4. 成功提示音（不等待返回）
    native.app.playSound('checkout-success', { requiresReturn: false })
    
    // 5. 批量更新本地统计（只需最后一个返回确认）
    await updateLocalStats([
      { type: 'sale', amount: order.totalAmount },
      { type: 'transaction', count: 1 },
      { type: 'daily-summary', data: order }
    ])
    
    showMessage('订单创建成功', 'success')
    return order
    
  } catch (error) {
    console.error('订单创建失败:', error)
    native.app.playSound('error', { requiresReturn: false })
    showMessage('订单创建失败，请重试', 'error')
    throw error
  }
}

// 批量更新本地统计的辅助函数
const updateLocalStats = async (stats: Array<{type: string, amount?: number, count?: number, data?: any}>) => {
  for (let i = 0; i < stats.length; i++) {
    const stat = stats[i]
    const isLast = i === stats.length - 1
    
    // 只有最后一个操作需要返回值确认
    await native.local.updateStat(stat, {
      requiresReturn: isLast,
      timeout: isLast ? 5000 : 1000
    })
  }
}

// 使用统一事件监听系统监控硬件状态
native.printer.on('statusChanged', (params) => {
  if (params.paperStatus === 'EMPTY') {
    showMessage('打印机缺纸，请添加纸张', 'warning')
  }
})

native.cashDrawer.on('opened', (params) => {
  console.log('钱箱已打开')
})
```

## 📊 性能和可靠性提升

### 1. 智能缓存策略

| 数据类型 | 缓存策略 | 有效期 | 同步时机 |
|----------|----------|--------|----------|
| **商品信息** | 首次查询时缓存 | 24小时 | 每日凌晨批量更新 |
| **系统配置** | 启动时加载 | 永久 | 配置变更时更新 |
| **离线订单** | 实时保存 | 直到同步成功 | 网络恢复时立即同步 |
| **用户会话** | 内存缓存 | 会话期间 | 登录/登出时清理 |

### 2. 错误处理和降级策略

```typescript
// 智能降级处理
class ResilientService {
  async getProduct(barcode: string): Promise<Product | null> {
    // 1. 尝试本地缓存（最快）
    const cached = await native.cache.getProduct(barcode)
    if (cached && !this.isCacheExpired(cached)) {
      return cached
    }
    
    // 2. 尝试Java中台（在线）
    try {
      const online = await native.java.getProductByBarcode(barcode)
      if (online) {
        // 更新缓存
        await native.cache.updateProduct(online)
        return online
      }
    } catch (error) {
      console.warn('Java中台调用失败，使用缓存数据:', error)
    }
    
    // 3. 降级到过期缓存（离线容错）
    return cached || null
  }
}
```

## 🎯 迁移指南

### 1. 开发环境升级步骤

```bash
# 1. 更新前端依赖
cd pos-frontend
npm install uuid @types/uuid
npm update

# 2. 重新生成类型定义
npm run type-check

# 3. 更新C#项目引用
cd ../POSSystem.Desktop
dotnet add package Microsoft.Extensions.Http
dotnet restore

# 4. 重新创建数据库迁移
dotnet ef migrations add ConvertToCache
dotnet ef database update
```

### 2. 现有代码迁移

**Vue端迁移：**
```typescript
// 旧代码
const result = await webViewBridge.sendMessage(
  MessageType.HARDWARE_CONTROL,
  'print_receipt',
  orderData
)

// 新代码
const result = await native.printer.printTicket({
  orderId: orderData.orderId,
  content: orderData.content
})
```

**C#端迁移：**
```csharp
// 旧代码
public class MessageHandler
{
    public async Task HandleMessage(Message message) { }
}

// 新代码
public class JsonRpcHandler
{
    private readonly Dictionary<string, Func<object[], Task<object>>> _methods;
    
    public async Task<JsonRpcResponse> ProcessRequest(JsonRpcRequest request) { }
}
```

## ✅ 验证清单

在完成架构升级后，请验证以下功能：

### 前端验证
- [ ] TypeScript类型检查通过
- [ ] native对象方法调用正常
- [ ] 硬件事件监听正常
- [ ] 错误处理正确显示

### 后端验证
- [ ] JSON-RPC方法注册完整
- [ ] Java中台API调用正常
- [ ] 本地缓存读写正常
- [ ] 离线数据同步正常

### 集成验证
- [ ] 扫码枪事件处理正常
- [ ] 打印机调用成功
- [ ] 网络切换时降级正常
- [ ] 数据同步机制正常

## 📞 支持和反馈

如果在架构升级过程中遇到任何问题，可以：

1. **查看日志**：检查C#端和Vue端的错误日志
2. **调试模式**：启用开发模式进行详细调试
3. **性能监控**：使用内置的性能指标监控
4. **技术支持**：联系架构团队获取支持

---

**文档版本**: 1.1.0  
**更新时间**: 2024年12月  
**变更状态**: 架构升级完成 