// 导入 VitePress 的定义配置方法
import { defineConfig, loadEnv } from 'vitepress';

// 定义导航栏项目
const nav = [
  { text: '指南', link: '/guide/quickstart' },
  { text: '组件', link: '/components/' }
  // 若要添加顶部导航下拉菜单，请取消注释以下内容：
  /*
  {
    text: 'Dropdown Menu',
    items: [
      { text: 'Item A', link: '/item-1' },
      { text: 'Item B', link: '/item-2' },
      { text: 'Item C', link: '/item-3' }
    ]
  }
   */
];

// 定义侧边栏项目
const sidebar = {
  '/guide': [
    {
      text: '指南',
      items: [{ text: '快速开始', link: '/guide/quickstart' }]
    }
  ],
  '/components': [
    {
      text: '通用基础组件',
      items: [
        { text: '表格 Table', link: '/components/Table' },
        { text: '列表 List', link: '/components/List' }
      ]
    }
  ]
};

// 导出配置文件
export default ({ mode }) => {
  const env = loadEnv(mode, process.cwd()); // 获取.env文件里定义的环境变量
  return defineConfig({
    title: 'Frontend-docs', // 网站标题
    description: '浏览器描述', // 网站描述
    lang: 'cn-ZH', // 语言设置
    base: env.VITE_BASE_URL || './',
    lastUpdated: true, // 显示最后更新时间
    themeConfig: {
      outlineTitle: '快速导航', // 大纲标题
      logo: '/logo.png', // 网站 logo 路径
      siteTitle: 'Frontend-docs', // 网站标题
      outline: 3, // 大纲深度
      socialLinks: [
        { icon: 'github', link: 'https://code.trechina.cn/gitlab/public-lib/frontend-platform/frontend-maker/-/issues' }
      ], // 社交链接
      nav, // 应用导航栏配置
      sidebar // 应用侧边栏配置
    },
    vite: {
      server: {
        port: 8001 // 将端口更改为所需的端口号，例如 8000
      }
    }
  });
};
